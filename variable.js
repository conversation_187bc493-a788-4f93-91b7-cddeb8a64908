 let Quantity=0;
function showQuantity(){
 console.log("the cart quantity is  "+Quantity);
}
function inecreaseQuantityBy1(){
    Quantity++;
console.log("the cart quantity is   "+Quantity);
}
function resetQuantity(){
    console.log("the Quantity is reseting");
    Quantity=0;
    console.log("the cart quantity is "+Quantity);
}
function inecreaseQuantityBy2(){
    Quantity+=2
    console.log("the cart quantity is  "+ Quantity);
}
function inecreaseQuantityBY3(){
    Quantity+=3;
    console.log("the cart quantity is  "+ Quantity);
}
let ranim;
console.log(ranim);