<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="Rock-paper-scissor.css">
    <title>Rock Paper Scissor</title>
</head>
<body>
   <section class="game">
      <div class="title">
        <h1>Rock-Paper-Scissor</h1>
        <p>choose!</p>
      </div>
      <div class="conting">
         <button onclick="playGame('rock')"><i class="fa-solid fa-hand-back-fist"></i></button>
         <button onclick="playGame('paper')"><i class="fa-regular fa-file"></i></button>
         <button onclick="playGame('scissor')"><i class="fa-solid fa-hand-scissors"></i></button>
       </div>
       <div class="score">
         <p class="win">
            win: 0
         </p>
         <p class="lose">
            lose: 0
         </p>
         <p class="tie">
            tie: 0
         </p>
       </div>
      <button class="result"> the result</button>
   </section>
<body>
   <script src="Rock-paper-scissor.js" ></script>
</body>
</html>