const choices = ["rock", "paper", "scissor"];
let playerChoice;
let score = {
    win: 0,
    lose: 0,
    tie: 0
};
 
function playGame(playerChoice){
   let computerChoice = choices[Math.floor(Math.random()*choices.length)] ;
    if (playerChoice=== computerChoice){
        document.querySelector(".tie").innerHTML="tie: "+score.tie++;
         document.querySelector(".result").innerHTML="you tied";
    }
    else if ( playerChoice==="rock" && computerChoice==="scissor"){
        document.querySelector(".win").innerHTML="win: "+score.win++
        document.querySelector(".result").innerHTML="you win";
    }
    else if ( playerChoice==="rock" && computerChoice==="paper"){
       document.querySelector(".lose").innerHTML="lose: "+score.lose++
            document.querySelector(".result").innerHTML="you lose";
    }
    else if (playerChoice==="paper" && computerChoice==="scissor"){
      document.querySelector(".lose").innerHTML="lose: "+score.lose++
            document.querySelector(".result").innerHTML="you lose";
    }
    else if (playerChoice==="paper" && computerChoice==="rock"){
         document.querySelector(".win").innerHTML="win: "+score.win++
        document.querySelector(".result").innerHTML="you win";
    }
    else if (playerChoice==="scissor" && computerChoice==="paper"){
        document.querySelector(".win").innerHTML="win: "+score.win++
        document.querySelector(".result").innerHTML="you win";
    }
    else if (playerChoice==="scissor" && computerChoice==="rock"){
         document.querySelector(".lose").innerHTML="lose: "+score.lose++
        document.querySelector(".result").innerHTML="you lose";
    }

}
