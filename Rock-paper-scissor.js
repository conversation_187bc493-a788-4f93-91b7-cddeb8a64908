const choices = ["rock", "paper", "scissor"];
let playerChoice;
let score = {
    win: 0,
    lose: 0,
    tie: 0
};
 
function playGame(playerChoice){
   let computerChoice = choices[Math.floor(Math.random()*choices.length)] ;
    if (playerChoice=== computerChoice){
        score.tie++;
        document.querySelector(".tie").innerHTML="tie: "+score.tie;
         document.querySelector(".result").innerHTML="you tied";
    }
    else if ( playerChoice==="rock" && computerChoice==="scissor"){
        score.win++;
        document.querySelector(".win").innerHTML="win: "+score.win;
        document.querySelector(".result").innerHTML="you win";
    }
    else if ( playerChoice==="rock" && computerChoice==="paper"){
        score.lose++
       document.querySelector(".lose").innerHTML="lose: "+score.lose;
            document.querySelector(".result").innerHTML="you lose";
    }
    else if (playerChoice==="paper" && computerChoice==="scissor"){
        score.lose++;
      document.querySelector(".lose").innerHTML="lose: "+score.lose;
            document.querySelector(".result").innerHTML="you lose";
    }
    else if (playerChoice==="paper" && computerChoice==="rock"){
        score.win++;
         document.querySelector(".win").innerHTML="win: "+score.win;
        document.querySelector(".result").innerHTML="you win";
    }
    else if (playerChoice==="scissor" && computerChoice==="paper"){
        score.win++
        document.querySelector(".win").innerHTML="win: "+score.win;
        document.querySelector(".result").innerHTML="you win";
    }
    else if (playerChoice==="scissor" && computerChoice==="rock"){
        score.lose++;
         document.querySelector(".lose").innerHTML="lose: "+score.lose;
        document.querySelector(".result").innerHTML="you lose";
    }

}
