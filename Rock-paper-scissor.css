*{
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    scroll-behavior: smooth;
}
body{
    background: linear-gradient(  to right bottom, rgb(0,31,131),rgb(192,0,189));
    height: 100vh;
font-family: 'Pacifico', cursive;
      display: flex;                /* Add this */
    align-items: center;          /* Add this */
    justify-content: center;      /* Add this */
    margin: 0; 
}
.game{
    background-color: white;
    border-radius: 20px;
    border-color: gray;
    border-width: 1px;
    border-style: solid;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    width: 30%;
    height: 75%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start; /* Changed from center to flex-start */
    padding-top:5% ;
   
}
.title{
    width: 100%;
    text-align: center;
    align-items: flex-start;
    justify-content: flex-start;
    margin-bottom: 24px;
}
.title p{
    padding-top: 5%;
    font-size: 20px;
    color :rgb(159, 159, 245);
}
.conting{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 24px;
     gap: 12px;
}
.conting button{
    background-color: #f0f0f0;
    color: rgb(0, 0, 0);
    width: 90px;
    height: 65px;
    border: none;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    cursor: pointer;
   display: flex;
   transition: transform 0.3s ease;
    font-size: 24px;
  
   
}
.conting button:hover{
    transform: scale(1.05);
    border: 1px solid rgb(159, 159, 245);
}







