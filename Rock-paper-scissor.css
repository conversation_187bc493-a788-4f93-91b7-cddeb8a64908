*{
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    scroll-behavior: smooth;
}
body{
    background: linear-gradient(  to right bottom, rgb(0,31,131),rgb(192,0,189));
    height: 100vh;
font-family: 'Pacifico', cursive;
      display: flex;                /* Add this */
    align-items: center;          /* Add this */
    justify-content: center;      /* Add this */
    margin: 0; 
}
.game{
    background-color: white;
    border-radius: 20px;
    border-color: gray;
    border-width: 1px;
    border-style: solid;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    width: 30%;
    height: 75%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start; /* Changed from center to flex-start */
    padding-top:5% ;
   
}
.title{
    width: 100%;
    text-align: center;
    align-items: flex-start;
    justify-content: flex-start;
    margin-bottom: 24px;
}
.title p{
    font-weight: bold;
    padding-top: 5%;
    font-size: 20px;
    color :rgb(159, 159, 245);
}
.conting{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 40px;
     gap: 12px;
}
.conting button{
    background-color: #f0f0f0;
    color: rgb(0, 0, 0);
    width: 90px;
    height: 65px;
    border: none;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius:14px;
   
    font-size: 16px;
    cursor: pointer;
   display: flex;
   transition: transform 0.3s ease;
    font-size: 24px;
  
   
}
.conting button:hover{
    transform: scale(1.05);
    border: 1px solid rgb(159, 159, 245);
    box-shadow: 0 4px 15px rgba(0,31,131,0.3);
}

.conting button:active{
    background-color: rgb(0,31,131);
    color: white;
    transform: scale(0.95);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    border: 2px solid rgb(159, 159, 245);
}
.score{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap:12px;
    margin-bottom: 24px;
}
.score p{
    font-size: 16px;
    color: rgb(159, 159, 245);
    margin: 0 10px;
}
.result{
    display: flex;
    justify-content: center;
    align-items: center;
   margin-top:16px;
   height: 60px;
   width: 150px;
   background-color: white;
   color: rgb(0, 0, 0);
   border: 1px solid rgb(159, 159, 245);
   border-radius: 8px;
   font-size: 26px;
 

  

}








